# Contract Demo - PDF变量分析工具

## 项目简介

这是一个基于Spring Boot的PDF变量分析工具，能够读取PDF文件并提取其中的变量信息。项目整合了多个主流的开源PDF处理库，提供强大的PDF文本提取和变量分析功能。

## 功能特性

- 📄 **多PDF库支持**: 整合Apache PDFBox、iText7、OpenPDF等主流PDF处理库
- 🔍 **变量提取**: 自动识别PDF中的变量（格式：${1}、${2}、${3}...）
- ⚙️ **配置化**: 通过application.yml配置PDF文件路径
- 🚀 **自动分析**: 应用启动时自动分析PDF变量并输出到控制台
- 🌐 **REST API**: 提供HTTP接口进行PDF变量分析
- 📊 **详细报告**: 输出变量总数和每个变量的详细信息

## 技术栈

- **框架**: Spring Boot 2.6.13
- **Java版本**: JDK 1.8
- **PDF处理库**:
  - Apache PDFBox 2.0.29
  - iText7 7.2.5
  - OpenPDF 1.3.30
- **工具库**: Hutool 5.8.38

## 项目结构

```
src/main/java/com/kang/contractdemo/
├── ContractDemoApplication.java     # 主启动类
├── config/
│   └── ContractConfig.java          # PDF路径配置类
├── controller/
│   └── PdfController.java           # PDF处理REST控制器
├── service/
│   └── PdfVariableService.java      # PDF变量分析服务
└── util/
    └── PdfUtil.java                 # PDF工具类
```

## 配置说明

在 `src/main/resources/application.yml` 中配置PDF文件路径：

```yaml
# PDF 模板路径
contract:
  path: /Users/<USER>/Desktop/contract-template.pdf
```

## 使用方法

### 1. 启动应用

```bash
mvn spring-boot:run
```

应用启动后会自动分析配置的PDF文件，并在控制台输出分析结果。

### 2. REST API接口

#### 分析PDF变量
```
GET /api/pdf/analyze
```

响应示例：
```json
{
  "success": true,
  "message": "PDF变量分析成功",
  "data": {
    "filePath": "/Users/<USER>/Desktop/contract-template.pdf",
    "variableCount": 5,
    "variables": ["${1}", "${2}", "${3}", "${4}", "${5}"]
  }
}
```

#### 获取PDF路径
```
GET /api/pdf/path
```

响应示例：
```json
{
  "success": true,
  "message": "获取PDF路径成功",
  "data": {
    "pdfPath": "/Users/<USER>/Desktop/contract-template.pdf"
  }
}
```

## 变量格式说明

本工具识别的变量格式为：`#{数字}`

示例：
- `#{1}` - 变量1
- `#{2}` - 变量2
- `#{10}` - 变量10
- `#{999}` - 变量999

## 控制台输出示例

```
============================================================
		PDF变量分析结果
============================================================
PDF文件路径: /Users/<USER>/Desktop/contract-template.pdf
变量总数: 5 个

变量详情:
	1. #{1}
	2. #{2}
	3. #{3}
	4. #{4}
	5. #{5}

============================================================
分析完成！
============================================================
```

## 依赖管理

项目使用Maven进行依赖管理，主要依赖包括：

```xml
<!-- Apache PDFBox -->
<dependency>
    <groupId>org.apache.pdfbox</groupId>
    <artifactId>pdfbox</artifactId>
    <version>2.0.29</version>
</dependency>

<!-- iText7 -->
<dependency>
    <groupId>com.itextpdf</groupId>
    <artifactId>itext7-core</artifactId>
    <version>7.2.5</version>
    <type>pom</type>
</dependency>

<!-- OpenPDF -->
<dependency>
    <groupId>com.github.librepdf</groupId>
    <artifactId>openpdf</artifactId>
    <version>1.3.30</version>
</dependency>
```

## 注意事项

1. 确保PDF文件路径正确且文件存在
2. PDF文件需要是可读取的文本格式（非扫描件）
3. 变量格式必须严格按照 `${数字}` 的格式
4. 应用启动时会自动进行一次PDF分析

## 扩展功能

- 支持多种PDF库，可根据需要选择最适合的库
- 可扩展支持其他变量格式
- 可添加PDF生成和编辑功能
- 可集成更多PDF处理特性

## 开发者

- 项目作者：Kang
- 技术支持：基于Spring Boot + 多PDF库整合