import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.font.PDType1Font;

public class CreateTestPdf {
    public static void main(String[] args) {
        try {
            // 创建一个新的PDF文档
            PDDocument document = new PDDocument();

            // 创建第一页
            PDPage page1 = new PDPage();
            document.addPage(page1);

            PDPageContentStream contentStream1 = new PDPageContentStream(document, page1);
            contentStream1.beginText();
            contentStream1.setFont(PDType1Font.HELVETICA, 12);
            contentStream1.newLineAtOffset(50, 750);
            contentStream1.showText("Contract Template - Page 1");
            contentStream1.newLineAtOffset(0, -30);
            contentStream1.showText("Party A Name: ${1}");
            contentStream1.newLineAtOffset(0, -20);
            contentStream1.showText("Party B Name: ${2}");
            contentStream1.newLineAtOffset(0, -20);
            contentStream1.showText("Contract Amount: ${3}");
            contentStream1.newLineAtOffset(0, -20);
            contentStream1.showText("Signing Date: ${4}");
            contentStream1.endText();
            contentStream1.close();

            // 创建第二页
            PDPage page2 = new PDPage();
            document.addPage(page2);

            PDPageContentStream contentStream2 = new PDPageContentStream(document, page2);
            contentStream2.beginText();
            contentStream2.setFont(PDType1Font.HELVETICA, 12);
            contentStream2.newLineAtOffset(50, 750);
            contentStream2.showText("Contract Template - Page 2");
            contentStream2.newLineAtOffset(0, -30);
            contentStream2.showText("Project Name: ${5}");
            contentStream2.newLineAtOffset(0, -20);
            contentStream2.showText("Project Address: ${6}");
            contentStream2.newLineAtOffset(0, -20);
            contentStream2.showText("Contact Phone: ${7}");
            contentStream2.newLineAtOffset(0, -20);
            contentStream2.showText("Remarks: ${8}");
            contentStream2.endText();
            contentStream2.close();

            // 创建第三页
            PDPage page3 = new PDPage();
            document.addPage(page3);

            PDPageContentStream contentStream3 = new PDPageContentStream(document, page3);
            contentStream3.beginText();
            contentStream3.setFont(PDType1Font.HELVETICA, 12);
            contentStream3.newLineAtOffset(50, 750);
            contentStream3.showText("Contract Template - Page 3");
            contentStream3.newLineAtOffset(0, -30);
            contentStream3.showText("Payment Method: ${9}");
            contentStream3.newLineAtOffset(0, -20);
            contentStream3.showText("Penalty: ${10}");
            contentStream3.newLineAtOffset(0, -20);
            contentStream3.showText("Other Terms: ${11}");
            contentStream3.endText();
            contentStream3.close();

            // 保存PDF文件
            String outputPath = "/Users/<USER>/Desktop/test-contract.pdf";
            document.save(outputPath);
            document.close();

            System.out.println("Test PDF file created: " + outputPath);

        } catch (Exception e) {
            System.err.println("Failed to create PDF: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
