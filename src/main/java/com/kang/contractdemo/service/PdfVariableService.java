package com.kang.contractdemo.service;

import com.kang.contractdemo.config.ContractConfig;
import com.kang.contractdemo.util.PdfUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * PDF变量分析服务
 */
@Service
public class PdfVariableService {

    private static final Logger logger = LoggerFactory.getLogger(PdfVariableService.class);

    @Autowired
    private ContractConfig contractConfig;

    /**
     * 应用启动后自动分析PDF变量
     */
    @PostConstruct
    public void analyzePdfVariablesOnStartup() {
        try {
            logger.info("=== PDF变量分析开始 ===");

            String pdfPath = contractConfig.getPath();
            logger.info("PDF文件路径: {}", pdfPath);

            // 分析PDF变量
            PdfUtil.VariableAnalysisResult result = analyzePdfVariables();

            // 输出分析结果到控制台
            printAnalysisResult(result);

            logger.info("=== PDF变量分析完成 ===");

        } catch (Exception e) {
            logger.error("PDF变量分析失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 分析PDF变量
     *
     * @return 分析结果
     */
    public PdfUtil.VariableAnalysisResult analyzePdfVariables() {
        String pdfPath = contractConfig.getPath();
        return PdfUtil.analyzeVariables(pdfPath);
    }

    /**
     * 打印分析结果到控制台
     *
     * @param result 分析结果
     */
    private void printAnalysisResult(PdfUtil.VariableAnalysisResult result) {
        String separator = repeatString("=", 60);
        System.out.println("\n" + separator);
        System.out.println("\t\tPDF变量分析结果");
        System.out.println(separator);
        System.out.println("PDF文件路径: " + result.getFilePath());
        System.out.println("变量总数: " + result.getVariableCount() + " 个");
        System.out.println("\n变量详情:");

        if (result.getVariables().isEmpty()) {
            System.out.println("\t未找到任何变量");
        } else {
            for (int i = 0; i < result.getVariables().size(); i++) {
                System.out.printf("\t%d. %s%n", i + 1, result.getVariables().get(i));
            }
        }

        System.out.println("\n" + separator);
        System.out.println("分析完成！");
        System.out.println(separator + "\n");
    }

    /**
     * 重复字符串（Java 8兼容版本）
     *
     * @param str 要重复的字符串
     * @param count 重复次数
     * @return 重复后的字符串
     */
    private String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }

    /**
     * 获取PDF文件路径
     *
     * @return PDF文件路径
     */
    public String getPdfPath() {
        return contractConfig.getPath();
    }

    /**
     * 提取PDF前三页的文本内容
     *
     * @return PDF前三页的文本内容
     */
    public String extractFirstThreePages() {
        try {
            String pdfPath = contractConfig.getPath();
            logger.info("开始提取PDF前三页文本内容: {}", pdfPath);

            String text = PdfUtil.extractTextWithPDFBox(pdfPath, 1, 3);

            logger.info("PDF前三页文本提取完成，文本长度: {} 字符", text.length());

            return text;

        } catch (Exception e) {
            logger.error("提取PDF前三页文本失败: {}", e.getMessage(), e);
            throw new RuntimeException("提取PDF前三页文本失败: " + e.getMessage(), e);
        }
    }
}