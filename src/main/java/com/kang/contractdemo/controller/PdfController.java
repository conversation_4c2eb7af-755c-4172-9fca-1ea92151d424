package com.kang.contractdemo.controller;

import com.kang.contractdemo.service.PdfVariableService;
import com.kang.contractdemo.util.PdfUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * PDF处理控制器
 */
@RestController
@RequestMapping("/api/pdf")
public class PdfController {

    @Autowired
    private PdfVariableService pdfVariableService;

    /**
     * 分析PDF变量
     *
     * @return 分析结果
     */
    @GetMapping("/analyze")
    public Map<String, Object> analyzePdfVariables() {
        Map<String, Object> response = new HashMap<>();

        try {
            PdfUtil.VariableAnalysisResult result = pdfVariableService.analyzePdfVariables();

            response.put("success", true);
            response.put("message", "PDF变量分析成功");
            Map<String, Object> data = new HashMap<>();
            data.put("filePath", result.getFilePath());
            data.put("variableCount", result.getVariableCount());
            data.put("variables", result.getVariables());
            response.put("data", data);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "PDF变量分析失败: " + e.getMessage());
            response.put("data", null);
        }

        return response;
    }

    /**
     * 获取PDF文件路径
     *
     * @return PDF文件路径信息
     */
    @GetMapping("/path")
    public Map<String, Object> getPdfPath() {
        Map<String, Object> response = new HashMap<>();

        try {
            String pdfPath = pdfVariableService.getPdfPath();

            response.put("success", true);
            response.put("message", "获取PDF路径成功");
            Map<String, Object> data = new HashMap<>();
            data.put("pdfPath", pdfPath);
            response.put("data", data);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取PDF路径失败: " + e.getMessage());
            response.put("data", null);
        }

        return response;
    }

    /**
     * 获取PDF前三页的文本内容
     *
     * @return PDF前三页的文本内容
     */
    @GetMapping("/first-three-pages")
    public Map<String, Object> getFirstThreePages() {
        Map<String, Object> response = new HashMap<>();

        try {
            String text = pdfVariableService.extractFirstThreePages();

            response.put("success", true);
            response.put("message", "获取PDF前三页文本成功");
            Map<String, Object> data = new HashMap<>();
            data.put("text", text);
            data.put("textLength", text.length());
            response.put("data", data);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取PDF前三页文本失败: " + e.getMessage());
            response.put("data", null);
        }

        return response;
    }
}