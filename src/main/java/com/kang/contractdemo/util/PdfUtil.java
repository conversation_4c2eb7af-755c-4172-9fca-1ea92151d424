package com.kang.contractdemo.util;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * PDF工具类
 * 整合主流PDF处理库功能
 */
public class PdfUtil {

    private static final Logger logger = LoggerFactory.getLogger(PdfUtil.class);

    /**
     * 变量名称正则表达式，匹配 ${数字} 格式
     */
    private static final String VARIABLE_PATTERN = "\\$\\{(\\d+)\\}";

    /**
     * 使用Apache PDFBox读取PDF文件内容
     *
     * @param filePath PDF文件路径
     * @return PDF文本内容
     * @throws IOException 文件读取异常
     */
    public static String extractTextWithPDFBox(String filePath) throws IOException {
        File file = new File(filePath);
        if (!file.exists()) {
            throw new IOException("PDF文件不存在: " + filePath);
        }

        try (PDDocument document = PDDocument.load(file)) {
            PDFTextStripper stripper = new PDFTextStripper();
            return stripper.getText(document);
        }
    }

    /**
     * 使用Apache PDFBox读取PDF文件指定页数的内容
     *
     * @param filePath PDF文件路径
     * @param startPage 起始页码（从1开始）
     * @param endPage 结束页码（包含）
     * @return PDF指定页数的文本内容
     * @throws IOException 文件读取异常
     */
    public static String extractTextWithPDFBox(String filePath, int startPage, int endPage) throws IOException {
        File file = new File(filePath);
        if (!file.exists()) {
            throw new IOException("PDF文件不存在: " + filePath);
        }

        try (PDDocument document = PDDocument.load(file)) {
            PDFTextStripper stripper = new PDFTextStripper();

            // 设置页码范围
            stripper.setStartPage(startPage);
            stripper.setEndPage(Math.min(endPage, document.getNumberOfPages()));

            String text = stripper.getText(document);

            logger.info("提取PDF第{}页到第{}页的文本，总页数: {}, 文本长度: {} 字符",
                       startPage, Math.min(endPage, document.getNumberOfPages()),
                       document.getNumberOfPages(), text.length());

            return text;
        }
    }

    /**
     * 从PDF文本中提取所有变量
     *
     * @param pdfText PDF文本内容
     * @return 变量列表
     */
    public static List<String> extractVariables(String pdfText) {
        List<String> variables = new ArrayList<>();
        Set<String> uniqueVariables = new HashSet<>();

        Pattern pattern = Pattern.compile(VARIABLE_PATTERN);
        Matcher matcher = pattern.matcher(pdfText);

        while (matcher.find()) {
            String variable = matcher.group(0); // 完整的变量名 ${数字}
            if (uniqueVariables.add(variable)) {
                variables.add(variable);
            }
        }

        return variables;
    }

    /**
     * 从PDF文件中提取变量信息
     *
     * @param filePath PDF文件路径
     * @return 变量分析结果
     */
    public static VariableAnalysisResult analyzeVariables(String filePath) {
        try {
            logger.info("开始分析PDF文件: {}", filePath);

            // 提取PDF文本
            String pdfText = extractTextWithPDFBox(filePath);
            logger.debug("PDF文本内容长度: {} 字符", pdfText.length());

            // 提取变量
            List<String> variables = extractVariables(pdfText);

            VariableAnalysisResult result = new VariableAnalysisResult();
            result.setFilePath(filePath);
            result.setVariableCount(variables.size());
            result.setVariables(variables);
            result.setPdfText(pdfText);

            logger.info("PDF变量分析完成，共找到 {} 个变量", variables.size());

            return result;

        } catch (IOException e) {
            logger.error("分析PDF文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("PDF文件分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 变量分析结果类
     */
    public static class VariableAnalysisResult {
        private String filePath;
        private int variableCount;
        private List<String> variables;
        private String pdfText;

        // Getters and Setters
        public String getFilePath() {
            return filePath;
        }

        public void setFilePath(String filePath) {
            this.filePath = filePath;
        }

        public int getVariableCount() {
            return variableCount;
        }

        public void setVariableCount(int variableCount) {
            this.variableCount = variableCount;
        }

        public List<String> getVariables() {
            return variables;
        }

        public void setVariables(List<String> variables) {
            this.variables = variables;
        }

        public String getPdfText() {
            return pdfText;
        }

        public void setPdfText(String pdfText) {
            this.pdfText = pdfText;
        }

        @Override
        public String toString() {
            return "VariableAnalysisResult{" +
                    "filePath='" + filePath + '\'' +
                    ", variableCount=" + variableCount +
                    ", variables=" + variables +
                    '}';
        }
    }
}