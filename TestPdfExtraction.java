import com.kang.contractdemo.util.PdfUtil;

public class TestPdfExtraction {
    public static void main(String[] args) {
        try {
            String pdfPath = "/Users/<USER>/Desktop/test-contract.pdf";

            System.out.println("=== 开始提取PDF前三页文本内容 ===");

            // 先提取整个PDF文本看看
            String fullText = PdfUtil.extractTextWithPDFBox(pdfPath);
            System.out.println("整个PDF文本长度: " + fullText.length() + " 字符");

            if (fullText.length() > 500) {
                System.out.println("整个PDF文本内容（前500字符）：");
                System.out.println(fullText.substring(0, 500));
            } else {
                System.out.println("整个PDF文本内容：");
                System.out.println(fullText);
            }

            // 提取前三页文本
            String text = PdfUtil.extractTextWithPDFBox(pdfPath, 1, 3);

            System.out.println("PDF前三页文本内容：");
            System.out.println("==========================================");
            System.out.println(text);
            System.out.println("==========================================");
            System.out.println("文本长度: " + text.length() + " 字符");

            // 分析变量
            System.out.println("\n=== 开始分析变量 ===");
            java.util.List<String> variables = PdfUtil.extractVariables(text);
            System.out.println("找到的变量数量: " + variables.size());
            System.out.println("变量列表: " + variables);

        } catch (Exception e) {
            System.err.println("错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
